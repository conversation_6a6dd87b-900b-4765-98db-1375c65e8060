import pandas as pd
import re


missing_salesman = set()


def flag_df(val):
    if "fred" in val:
        return 'fredericksburg store'
    elif "malibu" in val:
        return 'malibu store'
    else:
        return ""


def get_product_status(x):
    if x == "A":
        return "Active"
    elif x == "C":
        return "Cancelled"
    elif x == "IP":
        return "In Production"
    elif x == "IT":
        return "In Transit"
    elif x == "U":
        return "Upcoming"
    elif x == "I":
        return "Inactive"
    elif x == "R":
        return "Restock"
    else:
        return None


licensing_map = {
    "ACDC": "acdc",
    "BS": "black sabbath",
    "DF": "donovan frankenreiter",
    "FM": "fleetwood mac",
    "FRIDA": "frida kahlo",
    "LRND": "lauren daigle",
    "LSTAR": "lone star (pearl)",
    "NY": "neil young",
    "OZZY": "ozzy osbourne",
    "TGD": "the grateful dead",
    "VVG": "vincent van gogh",
    "WN": "willie nelson"
}


quarternary_map = {
    "K-ANIMAL": "animal",
    "K-ART": "art",
    "K-ASTRO": "astronomy",
    "K-FRUIT": "fruit",
    "K-LOVE": "love",
    "K-MUSIC": "music",
    "K-NA": "native american",
    "K-OCEAN": "ocean",
    "K-PEACE": "peace",
    "K-RELIGION": "religious",
    "L-ANIMAL": "animal",
    "L-ART": "art",
    "L-ASTRO": "astronomy",
    "L-FRUIT": "fruit",
    "L-LOVE": "love",
    "L-MUSIC": "music",
    "L-NA": "native american",
    "L-OCEAN": "ocean",
    "L-PEACE": "peace",
    "L-RELIGION": "religious",
    "O-ANIMAL": "animal",
    "O-ART": "art",
    "O-ASTRO": "astronomy",
    "O-FRUIT": "fruit",
    "O-LOVE": "love",
    "O-MUSIC": "music",
    "O-NA": "native american",
    "O-OCEAN": "ocean",
    "O-PEACE": "peace",
    "O-RELIGION": "religious",
    "W-ANIMAL": "animal",
    "W-ART": "art",
    "W-ASTRO": "astronomy",
    "W-FRUIT": "fruit",
    "W-LOVE": "love",
    "W-MUSIC": "music",
    "W-NA": "native american",
    "W-OCEAN": "ocean",
    "W-PEACE": "peace",
    "W-RELIGION": "religious"
}


salesman_dict = {
    "AG": "Alec Gellis",
    "BS": "Bree Statley",
    "BU": "Christian Clanton",
    "CC": "Caroline Chandler",
    "CG": "Christi Graham",
    "DH": "Darcy Hannah",
    "DK": "Denise Kubala",
    "DS": "Danica Stewart",
    "EH": "Erin Honaker",
    "EJ": "Eden Johnson",
    "HS": "House Account",
    "IM": "Ilce Maldonado",
    "JN": "Jama Honaker Nacke",
    "KO": "Katie Olsen",
    "KS": "Kathy Schettler",
    "KT": "Kendall Tipton",
    "LD": "Lindsey Dempsey",
    "LH": "Leslie Honaker",
    "LK": "Lori Kroh",
    "LR": "Lyndsey Riley",
    "LS": "Linda Shaich",
    "LW": "Lesa Welp",
    "MD": "Michal Douthit",
    "MS": "Marisa Scott",
    "NK": "Nichole Kuhlman",
    "NV": "Natalie Valle",
    "OS": "Olena Suprun",
    "RH": "Red Hakanson",
    "SH": "Sheila Hull",
    "TK": "Tara Kelley",
    "TR": "Tami Rodgers",
    "WO": "Web Order"
}


def name_salesman(val):
    if val.upper() in salesman_dict.keys():
        return salesman_dict[val.upper()]
    else:
        missing_salesman.add(val)
        return val


def find_tags(df):
    if df["Licensing"] in licensing_map.keys():
        return licensing_map[df["Licensing"]]
    elif df["Quaternary Category"] in quarternary_map.keys():
        return quarternary_map[df["Quaternary Category"]]
    else:
        return ""


def check_size(val):
    if str(val).endswith('"'):
        return val
    elif isinstance(val, (int, float)):
        return str(val) + '"'
    else:
        return ""


style_in = pd.read_excel(r"C:\Users\<USER>\Documents\Exenta Files - 11 August\1000107047 Style Mapping - LOREN Uphance.xlsx") # 1000107047 Style Mapping - LOREN Uphance
style_image_input = pd.read_excel(r"C:\Users\<USER>\Documents\Exenta Files - 11 August\1000100900 ProdMaintenance AllImages - Dev.xlsx") # 1000100900 ProdMaintenance AllImages - Dev
style_in['UPC'] = pd.to_numeric(style_in['UPC'], downcast='integer', errors='coerce')
style_image_input['UPC'] = pd.to_numeric(style_image_input['UPC'], downcast='integer', errors='coerce')
style_image_input = style_image_input[style_image_input['UPC'].notna()]
# style_custom_fields = pd.read_excel(r"C:\Users\<USER>\Downloads\Custom_fields_exenta_16_7_2025.xlsx")

style_out_df = pd.read_excel(r"Uphance Product Template.xlsx")
style_out = pd.DataFrame({col: pd.Series(dtype=style_out_df[col].dtype) for col in style_out_df.columns})

style_input = pd.merge(style_in, style_image_input, how='left', on='UPC')
# style_input = pd.merge(style_input, style_custom_fields, how='left', on='UPC_x')

style_input = style_input[style_input["Style_x"] != -1]

# 2. Ensure every cell is a string
style_input['Header Comments'] = style_input['Header Comments'].fillna('')

# 3. Replace literal “\n” sequences with real newlines
style_input['Header Comments'] = style_input['Header Comments'].str.replace(r'\\n', '\n',
                                                         regex=True)

# 4. Normalize all CRLF or CR-only endings to LF
style_input['Header Comments'] = style_input['Header Comments']\
    .str.replace(r'\r\n?', '\n', regex=True)


# Prepare columns
measurement_cols = [
    'Front Length', 'Back Length', 'Bust', 'Cancelled Date', 'Across Back',
    'Design Lead', 'Product Developer', 'Front Rise', 'Waist/Width',
    'Sleeve Length', 'Arm Hole', 'Hem', 'Inseam', 'Hips',
    'Across Shoulder', 'Leg Hole', 'Sleeve Opening'
]
# for col in measurement_cols:
#     style_input[col] = pd.NA

# Normalize comments
comments = (
    style_input['Header Comments']
      .fillna('')
      .astype(str)
      .str.replace('[“”]', '"', regex=True)
      .str.replace('inches|inch|in', '', regex=True, flags=re.IGNORECASE)
      .str.replace('around|across', '', regex=True, flags=re.IGNORECASE)
      .str.replace('"', '', regex=True)
)

# Patterns
patterns = {
    'Waist/Width':      r'(?i)(?:Waist\s*\/\s*Width|Waist|Width)\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Hips':             r'(?i)Hips?\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Bust':             r'(?i)Bust\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Across Back':      r'(?i)Across\s*Back\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Across Shoulder':  r'(?i)Across\s*Shoulder\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Sleeve Length':    r'(?i)(?:Sleeve\s*Length|Length)\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Arm Hole':         r'(?i)Arm\s*Hole\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Leg Hole':         r'(?i)Leg\s*Hole\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Front Length':     r'(?i)Front\s*Length\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Back Length':      r'(?i)Back\s*Length\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Front Rise':       r'(?i)Front\s*Rise\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Hem':              r'(?i)Hem\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Inseam':           r'(?i)Inseam\s*[-–—:]\s*(\d+(?:\.\d+)?)',
    'Sleeve Opening':   r'(?i)Sleeve\s*Opening\s*[-–—:]\s*(\d+(?:\.\d+)?)',
}

# Extraction using Series.items() instead of iteritems()
for idx, text in comments.items():
    for part in re.split(r'[\n/]', text):
        for col, pat in patterns.items():
            m = re.search(pat, part)
            if m:
                style_input.at[idx, col] = str(float(m.group(1))) + "\""

style_out["product_identifier"] = style_input["Style_x"]
style_out["category"] = style_input["Products Category"]
style_out["sub_category"] = style_input["Products Sub-Category"]
style_out["name"] = style_input["Style Name"]
style_out["product_status"] = style_input["Status"].apply(get_product_status)
style_out["description"] = style_input["Style Desc"]
style_out["sizing_system"] = style_input["Size_x"]
style_out["season"] = style_input["Season"].apply(lambda x: x if str(x).strip() != '' else 'NS')
# Color should never be blank, should be "None"
style_out["color"] = style_input["Color Desc"].fillna("None").apply(lambda x: x if str(x).strip() != '' else 'None')
style_out["color_code"] = style_input["Color_x"].fillna("None").apply(lambda x: x if str(x).strip() != '' else 'None')
style_out["Storefront Sold"] = style_input["Header Comments"].apply(flag_df)
style_out["Wholesale 50/50 available"] = style_out["season"].apply(lambda x: True if x not in ["MPT", "UNQ"] else False)
style_out["Wholesale 50/50 selling_price"] = style_input["Wholesale-Purchased"]
style_out["Wholesale 50/50 msrp"] = style_input["Wholesale-Purchased"]
style_out["Wholesale 60/40 available"] = style_out["season"].apply(lambda x: True if x not in ["MPT", "UNQ"] else False)
style_out["Wholesale 60/40 selling_price"] = style_input["Wholesale-Consigned"]
style_out["Wholesale 60/40 msrp"] = style_input["Wholesale-Consigned"]
style_out["Retail available"] = True
style_out["Retail selling_price"] = style_input["Retail Sale Price"]
style_out["Retail msrp"] = style_input["Retail Sale Price"]
# Replace "100% COTTON" with "100% Cotton"
style_out["textile_content"] = style_input["Content Desc"].str.replace("100 % COTTON", "100% Cotton").replace("100% COTTON", "100% Cotton")
style_out["weight"] = style_input["Inner Weight/LBS"]
style_out["weight_unit"] = "LBS"
style_out["tariff_code"] = style_input["HS Number"]
style_out["country_of_origin"] = style_input["Country of Origin"]
style_out["mfg_cost"] = style_input["Cost to MP"]
# should be USD
style_out["mfg_currency"] = "USD"
style_out["manufacturer"] = style_input["Country of Origin"].apply(lambda x: "Gend Silaee Pvt. Ltd." if x == "IN" else "Abana (Mauritius) Ltd." if x == "MU" else "")
style_out["image_url"] = style_input["URI"]
style_out["cover"] = style_input["Primary Image"].apply(lambda x: "TRUE" if x == "Y" else "FALSE")


style_out["plm design #"] = style_input["PLM Design"]
style_out["pattern #"] = style_input["Pattern #"].apply(lambda x: x if str(x).strip() != "0" and str(x).strip() != "&" else "")
style_out["cancelled date"] = style_input["Cancelled Date"]
style_out["design lead"] = style_input["Design Lead"].apply(name_salesman)
style_out["product developer"] = style_input["Product Developer"].apply(name_salesman)
style_out["designer"] = style_input["Designer"].apply(name_salesman)
style_out["front length"] = style_input["Front Length"].apply(check_size)
style_out["back length"] = style_input["Back Length"].apply(check_size)
style_out["bust"] = style_input["Bust"].apply(check_size)
style_out["across back"] = style_input["Across Back"].apply(check_size)
style_out["front rise"] = style_input["Front Rise"].apply(check_size)
style_out["waist/width"] = style_input["Waist/width"].apply(check_size)
style_out["sleeve length"] = style_input["Sleeve Length"].apply(check_size)
style_out["arm hole"] = style_input["Arm Hole"].apply(check_size)
# style_out["hem"] = ""
style_out["inseam"] = style_input["Inseam"].apply(check_size)
style_out["hips"] = style_input["Hips"].apply(check_size)
style_out["across shoulder"] = style_input["Across Shoulder"].apply(check_size)
# style_out["leg hole"] = ""
# style_out["sleeve opening"] = style_input["Designer"]

style_out["tags"] = style_input.apply(find_tags, axis=1)
style_out["variation_identifier"] = style_out["product_identifier"].str.cat(style_out['color_code'], sep='-', na_rep='')

style_out = style_out[~style_out["category"].isin(["TRIM", "CHEMICALS", "TOOLS", "ACC", "FABRIC"])]

style_out.to_excel(r"Generated/Uphance_Product_Data_11August.xlsx", index=False)


print(missing_salesman)
print("Done")


# // product_identifier
# // color