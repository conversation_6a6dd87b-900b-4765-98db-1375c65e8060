import pandas as pd

mpt_invoice_exenta = pd.read_excel(r"C:\Users\<USER>\Downloads\Invoice-mpt-exenta.xlsx")
mpt_invoice_web = pd.read_csv(r"C:\Users\<USER>\Downloads\invoices.csv")

mpt_invoice_exenta["invoice_id"] = mpt_invoice_exenta["PO #"].str.replace(
    r'(?i)^mp\[?t\s*#?',    # (?i) → case-insensitive, ^mpt → “MPT” in any case, \s* → 0+ spaces, #? → optional “#”
    '',
    regex=True
).str.strip()

mpt_invoice_exenta["listing_id"] = mpt_invoice_exenta["invoice_id"].str.replace(r'\D+', '', regex=True)
mpt_invoice_exenta = mpt_invoice_exenta[mpt_invoice_exenta["listing_id"] != ""]
mpt_invoice_exenta["listing_id"] = mpt_invoice_exenta["listing_id"] .astype("int64")

mpt_in = mpt_invoice_web.merge(mpt_invoice_exenta, how="inner", left_on="ListingId", right_on="listing_id")
output_file = pd.read_excel(r"C:\Users\<USER>\Downloads\Uphance Sales Import Template.xlsx")

mpt_sales_out = pd.DataFrame({col: pd.Series(dtype=output_file[col].dtype) for col in output_file.columns})

mpt_sales_out["order_number"] = mpt_in["Order No."]
mpt_sales_out["external_order_id"] = mpt_in["ListingId"]
mpt_sales_out["customer_name"] = mpt_in["BillingFirstName"] + ' ' + mpt_in["BillingLastName"]
mpt_sales_out["season"] = ""
mpt_sales_out["channel"] = "MPT"
mpt_sales_out["order_date"] = mpt_in["Order Date"]
mpt_sales_out["start_ship_date"] = mpt_in["CreatedOn"]
mpt_sales_out["cancel_ship_date"] = ""
mpt_sales_out["shipping_cost"] = mpt_in["ShippingAmount"]
mpt_sales_out["tax_calc_method"] = "LINE_ITEM"

mpt_sales_out["items_total"] = mpt_in["InvoiceTotal"]
mpt_sales_out["items_tax"] = mpt_in["SalesTax"]
mpt_sales_out["subtotal"] = mpt_in["Subtotal"]
mpt_sales_out["total_tax"] = mpt_in["SalesTax"]
mpt_sales_out["grand_total"] = mpt_in["InvoiceTotal"]
mpt_sales_out["total_quantity"] = mpt_in["Ship Qty"]
mpt_sales_out["shipping_tax"] = ""
mpt_sales_out["default_tax_rate"] = ""
mpt_sales_out["payment_terms"] = "Due At Shipment"


mpt_sales_out["currency"] = mpt_in["Currency"]

mpt_sales_out["billing_contact"] = mpt_in["BuyerPhone"]
mpt_sales_out["shipping_contact"] = mpt_in["BuyerPhone"]

mpt_sales_out["billing_address_line_1"] = mpt_in["BillingStreet1"]
mpt_sales_out["billing_address_line_2"] = mpt_in["BillingStreet2"]
mpt_sales_out["billing_address_city"] = mpt_in["BillingCity"]
mpt_sales_out["billing_address_state"] = mpt_in["BillingStateRegion"]
mpt_sales_out["billing_address_country"] = mpt_in["BillingCountry"]
mpt_sales_out["billing_address_postcode"] = mpt_in["BillingZipPostal"]
mpt_sales_out["shipping_address_line_1"] = mpt_in["ShippingStreet1"]
mpt_sales_out["shipping_address_line_2"] = mpt_in["ShippingStreet2"]
mpt_sales_out["shipping_address_city"] = mpt_in["ShippingCity"]
mpt_sales_out["shipping_address_state"] = mpt_in["ShippingStateRegion"]
mpt_sales_out["shipping_address_country"] = mpt_in["ShippingCountry"]
mpt_sales_out["shipping_address_postcode"] = mpt_in["ShippingZipPostal"]
mpt_sales_out["product_name"] = mpt_in["Style Name"]
mpt_sales_out["product_identifier"] = mpt_in["Style"]
mpt_sales_out["ean_upc"] = mpt_in["UPC"]
mpt_sales_out["color"] = mpt_in["Color Desc"]
# mpt_sales_out["salesman_1"] = mpt_in["Salesman1 Name"]
# mpt_sales_out["salesman_2"] = mpt_in["Salesman2 Name"]
mpt_sales_out["unit_price"] = mpt_in["ListingOriginalPrice"]

mpt_sales_out["unit_tax"] = mpt_in["SalesTax"]
mpt_sales_out["size"] = mpt_in["Size"]
mpt_sales_out["quantity"] = mpt_in["Ship Qty"]
mpt_sales_out["fulfillment_status"] = "Shipped"
    # mpt_in["Open Amt."].apply(lambda x: "Shipped" if x == 0 else ""))
mpt_sales_out["warehouse"] = "MPT"
mpt_sales_out["notes"] = mpt_in["BuyerUserName"]
mpt_sales_out["exenta_invoice_id"] = mpt_in["Invoice No."]
mpt_sales_out["website_invoice_id"] = mpt_in["InvoiceId"]

mpt_sales_out.to_excel("Uphance_Sales_Orders_MPT_v2.xlsx", index=False)

print("Done")





