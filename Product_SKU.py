import numpy as np
import pandas as pd
from config import get_product_style_mapping_file, get_product_sku_output_file


def flag_df(val):
    if "fred" in val:
        return 'fredericksburg store'
    elif "malibu" in val:
        return 'malibu store'
    else:
        return ""


mp_categories = ["ADORNMENTS", "BODY", "BOTTOMS", "DRESSES", "GENERAL", "GIFT", "HOMEWARE", "OUTERWEAR", "SPECIALTY", "TOPS", ""]
style_input = pd.read_excel(get_product_style_mapping_file()) # 1000107047 Style Mapping - LOREN Uphance
style_out_df = pd.read_excel(r"Magnolia Pearl SKUs.xlsx")
# style_image_input = pd.read_excel(r"C:\Users\<USER>\Documents\Exenta Files - 8 July\ProductImage.xlsx")
style_out = pd.DataFrame({col: pd.Series(dtype=style_out_df[col].dtype) for col in style_out_df.columns})
# style_input['UPC'] = pd.to_numeric(style_input['UPC'], downcast='integer', errors='coerce')
# style_image_input['UPC'] = pd.to_numeric(style_image_input['UPC'], downcast='integer', errors='coerce')
# style_image_input = style_image_input[style_image_input['UPC'].notna()]

# style_input = pd.merge(style_in, style_image_input, how='left', on='UPC')

style_out["product_identifier"] = style_input["Style"]
style_out["product_name"] = style_input["Style Name"]
style_out["product_name"] = style_out["product_name"].str.strip()
style_out.loc[style_out['product_name'] == '', 'product_name'] = np.nan
style_out['product_name'] = style_out['product_name'].fillna(style_out['product_identifier'])

style_out["season"] = style_input["Season"].apply(lambda x: x if x.strip() != '' else 'NS')
style_out["category"] = style_input["Products Category"]
style_out["category"] = style_out["category"].fillna("")

style_out["sub_category"] = style_input["Products Sub-Category"]
style_out["manufacturer"] = style_input["Country of Origin"].apply(lambda x: "Gend Silaee Pvt. Ltd." if x == "IN" else "Abana (Mauritius) Ltd." if x == "MU" else "")
# Color should not be blank, should be "None"
style_out["color"] = style_input["Color Desc"].fillna("None").apply(lambda x: x if str(x).strip() != '' else 'None')
style_out["color_code"] = style_input["Color"].fillna("None").apply(lambda x: x if str(x).strip() != '' else 'None')
style_out["size"] = style_input["Size"]
style_out["sku_#"] = style_input["Style"] + "-" + style_input["Color"] + "-" + style_input["Size"]
style_out["upc"] = style_input["UPC"]
# style_out["upc"] = style_out["upc"].str.strip()
# style_out.loc[style_out['upc'] == '', 'upc'] = np.nan
# style_out['upc'] = style_out['upc'].fillna(style_out['product_identifier'])

# Replace "100% COTTON" with "100% Cotton"
style_out["textile_content"] = style_input["Content Desc"].str.replace("100 % COTTON", "100% Cotton").replace("100% COTTON", "100% Cotton")
style_out["weight"] = style_input["Inner Weight/LBS"]
style_out["weight_unit"] = "lb"
style_out["tariff_code"] = style_input["HS Number"]
# "MU" should be "MG"
style_out["country_of_origin"] = style_input["Country of Origin"].apply(lambda x: "MG" if x == "MU" else x)

style_out = style_out[style_out["category"].isin(mp_categories)]

# style_out["product_status"] = style_input["Status"].apply(lambda x: 'ACTIVE' if x != 'I' else 'INACTIVE')
# style_out["Storefront Sold"] = style_input["Header Comments"].apply(flag_df)
# style_out["Wholesale 50/50 available"] = True
# style_out["Wholesale 50/50 selling_price"] = style_input["Wholesale-Purchased"]
# style_out["Wholesale 50/50 msrp"] = style_input["Wholesale-Purchased"]
# style_out["Wholesale 60/40 available"] = True
# style_out["Wholesale 60/40 selling_price"] = style_input["Wholesale-Consigned"]
# style_out["Wholesale 60/40 msrp"] = style_input["Wholesale-Consigned"]
# style_out["Retail available"] = True
# style_out["Retail available"] = style_input["Wholesale-Consigned"]
# style_out["Retail selling_price"] = style_input["Retail Sale Price"]
# style_out["Retail msrp"] = style_input["Retail Sale Price"]
# style_out["mfg_cost"] = style_input["Cost to MP"]
# style_out["mfg_currency"] = "INR"
# style_out["image_url"] = style_input["URI"]
# style_out["cover"] = style_input["Primary Image"].apply(lambda x: "TRUE" if x == "Y" else "FALSE")
# style_out["tags"] = style_input["Licensing"]


style_out.to_excel(get_product_sku_output_file(), index=False)


print("Done")


# upc