import numpy as np
import pandas as pd
from config import get_production_order_file, get_production_order_output_file

prod_in = pd.read_excel(get_production_order_file()) # 1000107720 ProdMaintenance Full Production for Dev - TestUpha
output_file = pd.read_excel(r"Uphance Production Orders Import Template.xlsx")

prod_out = pd.DataFrame({col: pd.Series(dtype=output_file[col].dtype) for col in output_file.columns})

cancel_reason_map = {
    "COLRE": "MP rejected color",
    "CREJ": "GSPL rejected color",
    "FABRE": "MP rejected fabric",
    "FABRJ": "GSPL rejected fabric",
    "FABST": "Not enough material",
    "LICEN": "Licensing issue",
    "PRICE": "Cost is too high",
    "PRNTR": "MP rejected print",
    "QCREJ": "MP rejected quality",
    "QREJ": "GSPL rejected quality",
    "RCVD": "MP received",
    "SHORT": "MP changed quantity",
    "SHRT": "GSPL short on quantity",
    "SIZE": "MP rejected size",
    "SIZEC": "GSPL changed size",
    "SSN": "Outside of season window",
    "STCHG": "MP changed style",
}

prod_out["production_order_number"] = prod_in["Prod No"]
prod_out["manufacturer_name"] = "Gend Silaee Private Limited"
prod_out["create_date"] = prod_in["Order Date"]
prod_out["ex_factory_date"] = prod_in["Projected Ship Date"]
prod_out["in_warehouse_date"] = prod_in["Due Date"]
prod_out["items_total"] = prod_in["FOBCost"] * prod_in["Prod Line Qty"]
prod_out["items_total"] = prod_out["items_total"].fillna(0)
prod_out["items_tax"] = 0
prod_out["subtotal"] = prod_out["items_total"]
prod_out["total_tax"] = 0
prod_out["grand_total"] = prod_out["items_total"]

# prod_in['issue_qty'] = np.where(prod_in['Stage'] == "ISSUE", prod_in['Prod Line Qty'], np.nan)
# prod_out['quantity'] = (
#     prod_in
#     .groupby(['Prod No', 'Style'])['issue_qty']
#     .transform('first')
# )

prod_out["quantity"] = prod_in["Prod Line Qty"]
prod_out["tax_level"] = 0
prod_out["currency"] = "USD"
prod_out["delivery_address_line_1"] = "Magnolia Pearl Clothing"
prod_out["delivery_address_line_2"] = "461 Split Rail Crossing"
prod_out["delivery_address_city"] = "Fredericksburg"
prod_out["delivery_address_state"] = "TX"
prod_out["delivery_address_country"] = "US"
prod_out["delivery_address_postcode"] = "78624"
prod_out["production_address_line_1"] = "Gend Silaee Private Limited"
prod_out["production_address_line_2"] = "68/1 Block A, DLF Industrial Area"
prod_out["production_address_line_3"] = "Sector 32"
prod_out["production_address_city"] = "Faridabad"
prod_out["production_address_state"] = "Haryana"
prod_out["production_address_country"] = "IN"
prod_out["production_address_postcode"] = "121003"
prod_out["tax_calc_method"] = "LINE_ITEM"
prod_out["product_name"] = prod_in["Style Name"]
prod_out["product_identifier"] = prod_in["Style"]
# Color should not be blank, should be "None".. 
prod_out["color"] = prod_in["Color Desc"].fillna("NONE").apply(lambda x: x if str(x).strip() != '' and x is not np.nan else 'NONE')
prod_out["unit_price"] = prod_in["FOBCost"]
prod_out["unit_tax"] = 0
prod_out["tax_level"] = 0
prod_out["size"] = prod_in["Size"]
# prod_out["quantity"] = prod_in["ProdLineWIP"]
prod_out["delivery_status"] = prod_in["Stage"].apply(lambda x: "not_received" if x != "REC" else "received")
prod_out["notes"] = prod_in["Remarks"]
prod_out["Routing Stage"] = prod_in["Stage"]
prod_out["WIP Quantity"] = prod_in["ProdLineWIP"]
prod_out["Line Item #"] = prod_in["Prod Line No"]
prod_out["Cancelled Date"] = prod_in["Cancelled Date"]
prod_out["Cancelled Quantity"] = prod_in["Prod Cancelled Qty"]
# This should be the cancelled reason description
prod_out["Cancel Reason"] = prod_in["Cancel Reason"].apply(lambda x: cancel_reason_map.get(x.strip().upper(), ""))


prod_out['max_line'] = (
    prod_out
    .groupby(['production_order_number', 'product_identifier'])['Line Item #']
    .transform('max')
)
prod_out['max_stage'] = np.where(
    prod_out['Line Item #'] == prod_out['max_line'],
    prod_out['Routing Stage'],
    np.nan
)
prod_out['max_stage'] = (
    prod_out
    .groupby(['production_order_number', 'product_identifier'])['Routing Stage']
    .transform('first')
)
prod_out = prod_out[prod_out['Routing Stage'] == prod_out['max_stage']].copy()
prod_out = prod_out.drop(columns=['max_line', 'max_stage'])
prod_out['total_quantity'] = (
    prod_out
    .groupby(['production_order_number'])['quantity']
    .transform('sum')
)
# prod_out.drop(columns=["Line Item #", "Cancelled Date"], inplace=True)

prod_out.to_excel(get_production_order_output_file(), index=False)



# Line Item #
# production_order_number