import pandas as pd

container_in = pd.read_excel(r"C:\Users\<USER>\Documents\Exenta Files - 11 August\1000108236 Container Detail for Dev - Uphance.xlsx") # 1000108236 Container Detail for Dev - Uphance
output_file = pd.read_excel(r"Uphance Report Container Template.xlsx")

container_out = pd.DataFrame({col: pd.Series(dtype=output_file[col].dtype) for col in output_file.columns})


container_out["production_order_number"] = container_in["Prod No"]
container_out["delivery_name"] = container_in["Container No"]
container_out["shipping_cost"] = 0
container_out["shipping_tax"] = 0
container_out["product_name"] = container_in["Style Name"]
container_out["product_identifier"] = container_in["Style"]
container_out["EAN"] = container_in["UPC"]
container_out["color"] = container_in["Color"]
container_out["unit_price"] = container_in["FOB Cost"]
container_out["unit_tax"] = 0
container_out["size"] = container_in["Size"]
container_out["quantity"] = container_in["Ship Qty"]
container_out["warehouse"] = "MPW"

container_out = container_out[((container_out['delivery_name'].notna()) &
                               (container_out['delivery_name'].str.len().eq(18)) &
                               (container_out['delivery_name'].str.isalnum()))]

container_out = container_out[container_out["production_order_number"] != 5004]


container_out.to_excel(r"Generated\Uphance_Container_Data_11August.xlsx", index=False)

# production_order_number
# EAN
# delivery_name
