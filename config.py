"""
Centralized Configuration for Uphance Exenta Migration Scripts

This file contains the centralized date configuration and helper functions
to generate consistent file paths across all scripts.

To change the date for all scripts, simply modify the DATE_STRING variable below.
"""

import os
from datetime import datetime

# ============================================================================
# MAIN CONFIGURATION - CHANGE DATE HERE
# ============================================================================

# Current date string used in file paths (e.g., "11August", "28July", "21July")
DATE_STRING = "11August"

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

def get_input_folder_path():
    """Get the input folder path with the current date"""
    return rf"C:\Users\<USER>\Documents\Exenta Files - {DATE_STRING}"

def get_generated_file_path(filename_prefix):
    """
    Get the path for generated files with the current date
    
    Args:
        filename_prefix (str): The prefix of the filename (e.g., "Uphance_Product_Data")
    
    Returns:
        str: Full path with date suffix
    """
    return rf"Generated\{filename_prefix}_{DATE_STRING}.xlsx"

def get_comparison_new_file_path(filename_prefix):
    """
    Get the path for comparison NEW files with the current date
    
    Args:
        filename_prefix (str): The prefix of the filename
    
    Returns:
        str: Full path for comparison NEW files
    """
    return rf"Comparison\New\{filename_prefix}_{DATE_STRING}_NEW.xlsx"

def get_comparison_updated_file_path(filename_prefix):
    """
    Get the path for comparison UPDATED files with the current date
    
    Args:
        filename_prefix (str): The prefix of the filename
    
    Returns:
        str: Full path for comparison UPDATED files
    """
    return rf"Comparison\Updated\{filename_prefix}_{DATE_STRING}_NEW_FULL.xlsx"

# ============================================================================
# SPECIFIC FILE PATH FUNCTIONS
# ============================================================================

def get_customer_input_file():
    """Get the customer input file path"""
    return os.path.join(get_input_folder_path(), "1000107769 Customers Test - Devs Uphance.xlsx")

def get_customer_messages_file():
    """Get the customer messages file path"""
    return os.path.join(get_input_folder_path(), "1000108140 Customer Messages - LOREN.xlsx")

def get_product_style_mapping_file():
    """Get the product style mapping file path"""
    return os.path.join(get_input_folder_path(), "1000107047 Style Mapping - LOREN Uphance.xlsx")

def get_product_images_file():
    """Get the product images file path"""
    return os.path.join(get_input_folder_path(), "1000100900 ProdMaintenance AllImages - Dev.xlsx")

def get_production_order_file():
    """Get the production order input file path"""
    return os.path.join(get_input_folder_path(), "1000107720 ProdMaintenance Full Production for Dev - TestUpha.xlsx")

def get_container_detail_file():
    """Get the container detail input file path"""
    return os.path.join(get_input_folder_path(), "1000108236 Container Detail for Dev - Uphance.xlsx")

# ============================================================================
# OUTPUT FILE PATHS
# ============================================================================

def get_customer_output_file():
    """Get the customer output file path"""
    return get_generated_file_path("uphance_customer_data_updated")

def get_product_output_file():
    """Get the product output file path"""
    return get_generated_file_path("Uphance_Product_Data")

def get_product_sku_output_file():
    """Get the product SKU output file path"""
    return get_generated_file_path("Uphance_Product_SKU_Data")

def get_production_order_output_file():
    """Get the production order output file path"""
    return get_generated_file_path("Uphance_Production_Orders")

def get_container_output_file():
    """Get the container output file path"""
    return get_generated_file_path("Uphance_Container_Data")

# ============================================================================
# COMPARISON FILE PATHS
# ============================================================================

def get_customer_comparison_new_file():
    """Get the customer comparison NEW file path"""
    return get_comparison_new_file_path("uphance_customer_data_updated")

def get_customer_comparison_updated_file():
    """Get the customer comparison UPDATED file path"""
    return get_comparison_updated_file_path("uphance_customer_data_updated")

def get_product_comparison_new_file():
    """Get the product comparison NEW file path"""
    return get_comparison_new_file_path("Uphance_Product_Data")

def get_product_comparison_updated_file():
    """Get the product comparison UPDATED file path"""
    return get_comparison_updated_file_path("Uphance_Product_Data")

def get_product_sku_comparison_new_file():
    """Get the product SKU comparison NEW file path"""
    return get_comparison_new_file_path("Uphance_Product_SKU_Data")

def get_product_sku_comparison_updated_file():
    """Get the product SKU comparison UPDATED file path"""
    return get_comparison_updated_file_path("Uphance_Product_SKU_Data")

def get_production_order_comparison_new_file():
    """Get the production order comparison NEW file path"""
    return get_comparison_new_file_path("Uphance_Production_Orders")

def get_production_order_comparison_updated_file():
    """Get the production order comparison UPDATED file path"""
    return get_comparison_updated_file_path("Uphance_Production_Orders")

def get_container_comparison_new_file():
    """Get the container comparison NEW file path"""
    return get_comparison_new_file_path("Uphance_Container_Data")

def get_container_comparison_updated_file():
    """Get the container comparison UPDATED file path"""
    return get_comparison_updated_file_path("Uphance_Container_Data")

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def print_current_config():
    """Print the current configuration for verification"""
    print("=" * 60)
    print("CURRENT CONFIGURATION")
    print("=" * 60)
    print(f"Date String: {DATE_STRING}")
    print(f"Input Folder: {get_input_folder_path()}")
    print("\nGenerated Files:")
    print(f"  Customer: {get_customer_output_file()}")
    print(f"  Product: {get_product_output_file()}")
    print(f"  Product SKU: {get_product_sku_output_file()}")
    print(f"  Production Order: {get_production_order_output_file()}")
    print(f"  Container: {get_container_output_file()}")
    print("=" * 60)

if __name__ == "__main__":
    print_current_config()
