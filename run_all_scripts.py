"""
Master Script to Run All Uphance Exenta Migration Scripts

This script runs all the main data processing scripts and comparison scripts
in the correct order. It uses the centralized configuration from config.py.

Usage:
    python run_all_scripts.py

The script will:
1. Display current configuration
2. Run all main data processing scripts
3. Run all comparison scripts
4. Display summary of results
"""

import os
import sys
import subprocess
import time
from datetime import datetime
from config import print_current_config, DATE_STRING

def run_script(script_path, script_name):
    """
    Run a Python script and handle errors
    
    Args:
        script_path (str): Path to the script file
        script_name (str): Display name for the script
    
    Returns:
        bool: True if successful, False if failed
    """
    print(f"\n{'='*60}")
    print(f"RUNNING: {script_name}")
    print(f"Script: {script_path}")
    print(f"{'='*60}")
    
    if not os.path.exists(script_path):
        print(f"❌ ERROR: Script file not found: {script_path}")
        return False
    
    try:
        start_time = time.time()
        
        # Run the script
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, 
                              text=True, 
                              cwd=os.getcwd())
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ SUCCESS: {script_name} completed in {duration:.2f} seconds")
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return True
        else:
            print(f"❌ ERROR: {script_name} failed with return code {result.returncode}")
            if result.stderr:
                print("Error output:")
                print(result.stderr)
            if result.stdout:
                print("Standard output:")
                print(result.stdout)
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: Error running {script_name}: {str(e)}")
        return False

def check_required_files():
    """
    Check if all required template and input files exist
    
    Returns:
        bool: True if all files exist, False otherwise
    """
    print(f"\n{'='*60}")
    print("CHECKING REQUIRED FILES")
    print(f"{'='*60}")
    
    required_files = [
        "VAT NUMBER CUSTOMER.csv",
        "Uphance Customer Import Template.xlsx",
        "Magnolia Pearl SKUs.xlsx",
        "Uphance Production Orders Import Template.xlsx",
        "Uphance Report Container Template.xlsx"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            missing_files.append(file_path)
    
    # Check if Generated folder exists
    if not os.path.exists("Generated"):
        print("📁 Creating Generated folder...")
        os.makedirs("Generated")
        print("✅ Generated folder created")
    else:
        print("✅ Generated folder exists")
    
    # Check if Comparison folders exist
    comparison_folders = ["Comparison/New", "Comparison/Updated"]
    for folder in comparison_folders:
        if not os.path.exists(folder):
            print(f"📁 Creating {folder} folder...")
            os.makedirs(folder, exist_ok=True)
            print(f"✅ {folder} folder created")
        else:
            print(f"✅ {folder} folder exists")
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} required files. Please ensure all files are present.")
        return False
    
    print(f"\n✅ All required files are present!")
    return True

def main():
    """Main function to run all scripts"""
    
    print("🚀 UPHANCE EXENTA MIGRATION SCRIPT RUNNER")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Display current configuration
    print_current_config()
    
    # Check required files
    if not check_required_files():
        print("\n❌ Exiting due to missing required files.")
        return False
    
    # Define scripts to run in order
    main_scripts = [
        ("Customer.py", "Customer Data Processing"),
        ("Product.py", "Product Data Processing"),
        ("Product_SKU.py", "Product SKU Data Processing"),
        ("Production_Order.py", "Production Order Data Processing"),
        ("Container.py", "Container Data Processing")
    ]
    
    comparison_scripts = [
        ("Comparison/Customer.py", "Customer Data Comparison"),
        ("Comparison/Product.py", "Product Data Comparison"),
        ("Comparison/Product_SKU.py", "Product SKU Data Comparison"),
        ("Comparison/Production_Order.py", "Production Order Data Comparison"),
        ("Comparison/Container.py", "Container Data Comparison")
    ]
    
    # Track results
    results = {
        "main_scripts": {},
        "comparison_scripts": {}
    }
    
    # Run main scripts
    print(f"\n🔄 RUNNING MAIN DATA PROCESSING SCRIPTS")
    print("=" * 60)
    
    for script_path, script_name in main_scripts:
        success = run_script(script_path, script_name)
        results["main_scripts"][script_name] = success
        
        if not success:
            print(f"\n⚠️  WARNING: {script_name} failed. Continuing with remaining scripts...")
    
    # Run comparison scripts
    print(f"\n🔄 RUNNING COMPARISON SCRIPTS")
    print("=" * 60)
    
    for script_path, script_name in comparison_scripts:
        success = run_script(script_path, script_name)
        results["comparison_scripts"][script_name] = success
        
        if not success:
            print(f"\n⚠️  WARNING: {script_name} failed. Continuing with remaining scripts...")
    
    # Display final summary
    print(f"\n📊 FINAL SUMMARY")
    print("=" * 60)
    print(f"Date Configuration: {DATE_STRING}")
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Main scripts summary
    print(f"\n📋 Main Data Processing Scripts:")
    main_success = 0
    main_total = len(results["main_scripts"])
    
    for script_name, success in results["main_scripts"].items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"  {status}: {script_name}")
        if success:
            main_success += 1
    
    # Comparison scripts summary
    print(f"\n📋 Comparison Scripts:")
    comp_success = 0
    comp_total = len(results["comparison_scripts"])
    
    for script_name, success in results["comparison_scripts"].items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"  {status}: {script_name}")
        if success:
            comp_success += 1
    
    # Overall summary
    total_success = main_success + comp_success
    total_scripts = main_total + comp_total
    
    print(f"\n🎯 OVERALL RESULTS:")
    print(f"  Main Scripts: {main_success}/{main_total} successful")
    print(f"  Comparison Scripts: {comp_success}/{comp_total} successful")
    print(f"  Total: {total_success}/{total_scripts} successful")
    
    if total_success == total_scripts:
        print(f"\n🎉 ALL SCRIPTS COMPLETED SUCCESSFULLY!")
        print(f"📁 Generated files are in the 'Generated' folder with date suffix: {DATE_STRING}")
        print(f"📁 Comparison files are in 'Comparison/New' and 'Comparison/Updated' folders")
        return True
    else:
        failed_count = total_scripts - total_success
        print(f"\n⚠️  {failed_count} SCRIPT(S) FAILED. Please check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
