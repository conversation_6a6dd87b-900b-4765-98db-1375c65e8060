import pandas as pd
import re

missing_salesman = set()

customer_in = pd.read_excel(r"C:\Users\<USER>\Documents\Exenta Files - 11 August\1000107769 Customers Test - Devs Uphance.xlsx",
                            dtype={"Customer ID": str}) # 1000107769 Customers Test - Devs Uphance
customer_vat_file = pd.read_csv(r"VAT NUMBER CUSTOMER.csv",
                                dtype={"CUSTOMER": str}) # me dunga
customer_out_df = pd.read_excel(r"Uphance Customer Import Template.xlsx")
customer_out = pd.DataFrame({col: pd.Series(dtype=customer_out_df[col].dtype) for col in customer_out_df.columns})
customer_input = pd.merge(customer_in, customer_vat_file, how="left", left_on="Customer ID", right_on="CUSTOMER")
customer_messages = pd.read_excel(r"C:\Users\<USER>\Documents\Exenta Files - 11 August\1000108140 Customer Messages - LOREN.xlsx",
                                  dtype={"Customer ID": str})
customer_input = pd.merge(customer_input, customer_messages, how='left', on='Customer ID')
customer_input.rename(columns={
    "Customer ID_x": "Customer ID",
    "Customer Name_x": "Customer Name"
}, inplace=True)


invalid_char_re = re.compile(r'[^A-Za-z0-9\- ]')


salesman_dict = {
    "AG": "Alec Gellis",
    "BS": "Bree Statley",
    "BU": "Christian Clanton",
    "CC": "Caroline Chandler",
    "CG": "Christi Graham",
    "DH": "Darcy Hannah",
    "DK": "Denise Kubala",
    "DS": "Danica Stewart",
    "EH": "Erin Honaker",
    "EJ": "Eden Johnson",
    "HS": "House Account",
    "IM": "Ilce Maldonado",
    "JN": "Jama Honaker Nacke",
    "KO": "Katie Olsen",
    "KS": "Kathy Schettler",
    "KT": "Kendall Tipton",
    "LD": "Lindsey Dempsey",
    "LH": "Leslie Honaker",
    "LK": "Lori Kroh",
    "LR": "Lyndsey Riley",
    "LS": "Linda Shaich",
    "LW": "Lesa Welp",
    "MD": "Michal Douthit",
    "MS": "Marisa Scott",
    "NK": "Nichole Kuhlman",
    "NV": "Natalie Valle",
    "OS": "Olena Suprun",
    "RH": "Red Hakanson",
    "SH": "Sheila Hull",
    "TK": "Tara Kelley",
    "TR": "Tami Rodgers",
    "WO": "Web Order",
}


def name_salesman(val):
    if val.upper() in salesman_dict.keys():
        return salesman_dict[val.upper()]
    else:
        missing_salesman.add(val)
        return val


def channel_name(val):
    if "web" in val.lower():
        return 'WEB'
    elif "dtc" in val.lower() or "mpt" in val.lower():
        return 'DTC Purchase'
    elif "emp" in val.lower():
        return 'Wholesale 50/50'
    elif "whsl" in val.lower():
        return 'Wholesale 50/50'
    elif "retail" in val.lower():
        return "DTC Purchase"
    else:
        return "Wholesale 50/50"


def city_condition(x):
    return (
            x.strip() == "" or
            x.lower() == "idk" or
            x.lower() == "need" or
            x.lower() == "not" or
            x.lower() == "tbd" or
            x.lower() == "x" or
            x.lower() == "123" or
            x.lower() == "00" or
            x.lower() == "100" or
            x.lower() == "1" or
            x.lower() == "1234" or
            x.lower() == "?" or
            x.lower() == "_" or
            x.lower() == "."
    )


def invalid_character_clean(val):
    if invalid_char_re.search(str(val)):
        return ""
    else:
        if str(val) not in ["0", "00000", "000000", "WRX", "WV", "WY", "X"]:
            return val
        else:
            return ""


def clean_special_characters(val):
    return re.sub(r'[^A-Za-z0-9 ]+', '', val)


customer_input["City"] = customer_input["City"].astype(str)
customer_input["Cntry"] = customer_input["Cntry"].astype(str)

customer_out["name"] = customer_input["Customer Name"]
customer_out[['contact_first_name', 'contact_last_name']] = customer_input['Customer Name'].str.split(' ', n=1, expand=True)
customer_out["contact_last_name"] = customer_out["contact_last_name"].fillna("")
customer_out["country"] = customer_input["Cntry"].apply(lambda x: x if x.strip() != "" else "NONE")
customer_out["reference"] = customer_input["Customer ID"]
customer_out["city"] = customer_input["City"].apply(lambda x: "" if city_condition(x) else x)
customer_out["city"] = (customer_out["city"].apply(lambda x: x if x.lower() != "fred" else "Fredericksburg")
                        .str.replace(r'[^A-Za-z0-9 ]+', '', regex=True).apply(invalid_character_clean))
customer_out["customer_type"] = customer_input["Customer Type"].apply(lambda x: "wholesale" if x == "WHSL" else "retail")
# remove need or ? or "nee" or tbd or / in the below data
customer_out["address_line_1"] = customer_input["Address 1"].apply(lambda x: x if str(x).strip().lower() != "need"
                                                                                  and str(x).strip().lower() != "?"
                                                                                  and str(x).strip().lower() != "nee"
                                                                                  and str(x).strip().lower() != "tbd"
                                                                                  and str(x).strip().lower() != "/"
                                                                                else "")
customer_out["address_line_2"] = customer_input["Address 2"].apply(lambda x: x if str(x).strip().lower() != "need"
                                                                                  and str(x).strip().lower() != "?"
                                                                                  and str(x).strip().lower() != "nee"
                                                                                  and str(x).strip().lower() != "tbd"
                                                                                  and str(x).strip().lower() != "/" else "")
customer_out["address_city"] = (customer_input["City"].str.replace(r'[^A-Za-z0-9 ]+', '', regex=True).apply(lambda x: x if str(x).strip().lower() != "need"
                                                                                  and str(x).strip().lower() != "?"
                                                                                  and str(x).strip().lower() != "nee"
                                                                                  and str(x).strip().lower() != "tbd"
                                                                                  and str(x).strip().lower() != "/" else "").apply(invalid_character_clean))
customer_out["address_state"] = customer_input["St."].apply(lambda x: x if str(x).strip().lower() != "need"
                                                                                  and str(x).strip().lower() != "?"
                                                                                  and str(x).strip().lower() != "nee"
                                                                                  and str(x).strip().lower() != "tbd"
                                                                                  and str(x).strip().lower() != "/" else "")
customer_out["address_postcode"] = customer_input["Zip"].apply(lambda x: x if str(x).strip().lower() != "need"
                                                                                  and str(x).strip().lower() != "?"
                                                                                  and str(x).strip().lower() != "nee"
                                                                                  and str(x).strip().lower() != "tbd"
                                                                                  and str(x).strip().lower() != "/" else "").apply(invalid_character_clean)
customer_out["address_country"] = customer_input["Cntry"].apply(lambda x: x if str(x).strip().lower() != "need"
                                                                                  and str(x).strip().lower() != "?"
                                                                                  and str(x).strip().lower() != "nee"
                                                                                  and str(x).strip().lower() != "tbd"
                                                                                  and str(x).strip().lower() != "/" else "")
customer_out["address_default_for_billing"] = "FALSE"
customer_out["address_default_for_shipping"] = "FALSE"
customer_out["agent"] = customer_input["Slsmn 1"].apply(name_salesman)
customer_out["currency"] = "USD"
customer_out["vat_number"] = customer_input["VATNUMBER"]
customer_out["channel_name"] = customer_input["Customer Type"].apply(channel_name)
customer_out["default_payment_terms"] = "Due At Shipment"
customer_out["contact_email"] = customer_input["Email"]
customer_out["contact_phone_1"] = customer_input["Bus. Phone"]
customer_out["payment_discount"] = 0
# if country != US then Delivery Duties Paid
customer_out["incoterms"] = customer_input["Cntry"].apply(lambda x: "Delivery Duties Paid" if x != "US" else "")
customer_out["tags"] = customer_input["Customer ID"].apply(lambda x: "OG: Approved Discount" if x == 215 or x == 23 or x == 1592 else "")
customer_out["status"] = customer_input["Active"].apply(lambda x: "ACTIVE" if x == "Y" else "INACTIVE")
customer_out["notes"] = customer_input["Message"]

customer_out.to_excel(r"Generated\uphance_customer_data_updated_11August.xlsx", index=False)

print(missing_salesman)

# // reference