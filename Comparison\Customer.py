import hashlib

import pandas as pd
from config import get_customer_output_file, get_customer_comparison_new_file, get_customer_comparison_updated_file

sales_in_new = pd.read_excel(get_customer_output_file())
sales_in_old = pd.read_excel(r"Old Data\uphance_customer_data_updated.xlsx")

sales_in_new.drop_duplicates(inplace=True)
sales_in_old.drop_duplicates(inplace=True)

data_str = sales_in_new.fillna('').astype(str).agg('|'.join, axis=1)
sales_in_new['data_hash'] = data_str.apply(lambda s: hashlib.md5(s.encode('utf-8')).hexdigest())

data_str = sales_in_old.fillna('').astype(str).agg('|'.join, axis=1)
sales_in_old['data_hash'] = data_str.apply(lambda s: hashlib.md5(s.encode('utf-8')).hexdigest())

merged = (
    sales_in_new.merge(
         sales_in_old[['reference', 'data_hash']],
         on=['reference'], 
         how='outer',
         indicator=True,
         suffixes=('', '_old')
      )
)

# brand‐new rows in new_df
new_rows = merged[merged['_merge'] == 'left_only']

both = merged[merged['_merge'] == 'both'].copy()
changed_rows = both[both['data_hash_old'] != both['data_hash']]

# 5) if you want the full rows from your original new_df:
changed_full = sales_in_new[sales_in_new['reference'].isin(changed_rows['reference'])]
changed_full.drop(columns=["data_hash"], inplace=True)

if "data_hash" in new_rows.columns:
    new_rows.drop(columns=["data_hash"], inplace=True)
if "_merge" in new_rows.columns:
    new_rows.drop(columns=["_merge"], inplace=True)
if "data_hash_old" in new_rows.columns:
    new_rows.drop(columns=["data_hash_old"], inplace=True)

new_rows.to_excel(get_customer_comparison_new_file(), index=False)
changed_full.to_excel(get_customer_comparison_updated_file(), index=False)

print("Done")




