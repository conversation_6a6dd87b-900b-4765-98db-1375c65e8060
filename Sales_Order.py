import pandas as pd

sales_input = pd.read_excel(r"C:\Users\<USER>\Downloads\OrderDetailExenta16_07_2025.xlsx")
# input_map = {
#     "CustomerName": "Customer Name",
#     "Customer": "Customer ID",
#     "OrderNoCompany": "Order No.",
#     "OrderDate": "Order Date",
#     "Style": "Style",
#     "StyleName": "Style Name",
#     "StyleDesc": "Style Desc",
#     "Color": "Color",
#     "ColorDesc": "Color Desc",
#     "Size": "Size",
#     "OrderQty": "Qty Ord",
#     "UPC": "UPC",
#     "Season": "Season",
#     "BOLShipDate": "Ship Date",
#     "RequestDate": "Start Date",
#     "RequestDateDetail": "Start Date Dtl.",
#     "CancelDate": "Cancel Date",
#     "CancelDateDetail": "Cancel Date Dtl.",
#     "AllocatedQty": "Alloc Qty",
#     "TotalAmt": "Total Amount",
#     "TotalAmtWithoutCancel": "Total Amount without Cancel",
#     "TotalPackQty": "Total Pack Qty",
#     "TotalQtyWithoutCancel": "Total Quantity without Cancel",
#     "ItemSeason": "Item Season",
#     "Discount": "Discount",
#     "DiscountRate": "Discount %",
#     "DiscountDesc": "Discount Desc",
#     "OrderLineDiscAmtShip": "Ship Line Disc Amt",
#     "ShipAmt": "Ship Amt",
#     "OrderReference": "Order Reference",
#     "Currency": "Currency",
#     "CustomerType": "Customer Type",
#     "ShipToName": "ShipTo Name",
#     "ColorName": "Color Name",
#     "Price": "Price",
#     "Warehouse": "Warehouse",
#     "PickedQty": "Pick Qty",
#     "PKFactor": "PK Qty",
#     "OpenQty": "Open Qty",
#     "OpenMinusAlloc": "OpenMinusAllocQty",
#     "MinOrdQty": "Min Qty",
#     "MaxOrdQty": "Max Qty",
#     "CancelledQty": "Cancel Qty",
#     "EnsembleQty": "Ensemble Qty",
#     "EDIPackQty": "EDI Pack Qty.",
#     "InFactor": "IN Qty",
#     "OrderNo": "Order ID",
#     "ShipViaName": "Ship Via Name",
#     "ShipVia": "Ship Via",
#     "ShippedQty": "Ship Qty",
#     "Vendor": "Vendor",
#     "WIPQty": "WIP Qty",
#     "PriceCode": "Price Code",
#     "OrderOrigin": "Order Origin",
#     "OrderType": "Order Type",
#     "Terms": "Terms",
#     "TermsDiscountRate": "Terms Disc %",
#     "StyleStatus": "Status",
#     "Salesman1Name": "Salesman1 Name",
#     "Salesman2Name": "Salesman2 Name",
#     "Salesman1": "Slsmn 1",
#     "Salesman2": "Slsmn 2"
# }

sales_input = sales_input[sales_input["Customer ID"] != "99999"]
sales_input = sales_input[sales_input["Customer ID"] != 99999]
order_type_to_ignore = ["846UP", "CPU", "DMG", "LAUNDRY", "LIVE", "MPT", "SHOPIFY", "STRTRF", "TAXEST", "WEB"]
sales_input = sales_input[~sales_input["Order Type"].isin(order_type_to_ignore)]
invoice_df = pd.read_excel(r"C:\Users\<USER>\Downloads\Invoice_17_07_2025.xlsx")

sales_in = sales_input.merge(invoice_df, how="left", left_on="Order No.", right_on="Order No.")
output_file = pd.read_excel(r"C:\Users\<USER>\Downloads\Uphance Sales Import Template.xlsx")

sales_out = pd.DataFrame({col: pd.Series(dtype=output_file[col].dtype) for col in output_file.columns})


def decide_channel(df):
    if df["Customer Type_x"] == "WHSL":
        if df["Price Code_x"] == "A":
            if df["Order Origin_x"] == "NUORDER" or df["Order Type_x"] == "NUORDER":
                if df["Order Type_x"] in ["REGCB4", "REG4CB", "CBXRL", "CBXNO", "INTER"]:
                    return "NuOrder 100 CB"
                else:
                    return "NuOrder 100 PURCHASE"
            else:
                if df["Order Type_x"] in ["REGCB4", "REG4CB", "CBXRL", "CBXNO", "INTER"]:
                    return "WHSL 100 CB"
                else:
                    return "WHSL 100 PURCHASE"
        elif df["Price Code_x"] == "B":
            if df["Order Origin_x"] == "NUORDER" or df["Order Type_x"] == "NUORDER":
                if df["Order Type_x"] in ["NUORER", "REG", "REGXRL", "DIR", "CPU", "INTER"]:
                    return "NuOder 50/50 Purchase"
                elif df["Order Type_x"] in ["REGCB4", "REG4CB", "CBXRL", "CBXNO", "INTER"]:
                    return "NuOder 50/50 CB"
                else:
                    return "NuOder 50/50 Purchase"
            else:
                if df["Order Type_x"] in ["REG", "REGXRL", "DIR", "CPU", "INTER"]:
                    return "WHSL 50/50 Purchase"
                elif df["Order Type_x"] in ["REGCB4", "REG4CB", "CBXRL", "CBXNO", "DIR", "CPU"]:
                    return "WHSL 50/50 CB"
                else:
                    return "WHSL 50/50 Purchase"
        elif df["Price Code_x"] == "C":
            if df["Order Origin_x"] == "NUORDER" or df["Order Type_x"] == "NUORDER":
                return "NuOder 60/40 CB"
            else:
                return "WHSL 60/40 CB"
        else:
            return "Charity/Marketing/Legal"
    elif df["Customer Type_x"] == "DTC-I" or df["Customer Type_x"] == "DTC" or df["Customer Type_x"] == "WEB":
        if df["Price Code_x"] == "A":
            if df["Order Origin_x"] == "NUORDER" or df["Order Type_x"] == "NUORDER":
                if df["Order Type_x"] in ["CBXNO", "CBXRL", "DIR", "CPU", "REGCB4", "REG4CB"]:
                    return "NuOrder 100 CB"
                else:
                    return "NuOrder 100 PURCHASE"
            else:
                if df["Order Type_x"] in ["CBXNO", "CBXRL", "DIR", "CPU", "REGCB4", "REG4CB"]:
                    return "DTC CB"
                else:
                    return "DTC Purchase"
        elif df["Price Code_x"] == "B":
            if df["Order Origin_x"] == "NUORDER" or df["Order Type_x"] == "NUORDER":
                if df["Order Type_x"] in ["CBXNO", "CBXRL", "DIR", "CPU", "REGCB4", "REG4CB"]:
                    return "NuOrder DTC 50/50 CB"
                else:
                    return "NuOrder DTC 50/50 Purchase"
            else:
                if df["Order Type_x"] in ["CBXNO", "CBXRL", "DIR", "CPU", "REGCB4", "REG4CB"]:
                    return "DTC 50/50 CB"
                else:
                    return "DTC 50/50"
        elif df["Price Code_x"] == "C":
            if df["Order Origin_x"] == "NUORDER" or df["Order Type_x"] == "NUORDER":
                if df["Order Type_x"] in ["CBXNO", "CBXRL", "DIR", "CPU", "REGCB4", "REG4CB"]:
                    return "NuOrder DTC 60/40 CB"
                else:
                    return "NuOrder DTC 60/40 Purchase"
            else:
                if df["Order Type_x"] in ["CBXNO", "CBXRL", "DIR", "CPU", "REGCB4", "REG4CB"]:
                    return "DTC 60/40 CB"
                else:
                    return "DTC 60/40"
        else:
            return "Charity/Marketing/Legal"
    else:
        if df["Customer Type_x"] == "EMP":
            return "Employee"
        elif df["Order Type_x"] == "MKT":
            return "Charity/Marketing/Legal"
        else:
            return ""



    # if df["Customer Type_x"] == "WHSL" and df["Price Code_x"] == "B" and df["Order Origin_x"] == "NUORDER" and df["Order Type_x"] in ["NUORER", "REG", "REGXRL", "DIR", "CPU", "INTER"]:
    #     return "NuOder 50/50 Purchase"
    # elif df["Customer Type_x"] == "WHSL" and df["Price Code_x"] == "B" and df["Order Origin_x"] == "NUORDER" and df["Order Type_x"] in ["REGCB4", "REG4CB", "CBXRL", "CBXNO", "INTER"]:
    #     return "NuOder 50/50 CB"
    # elif df["Customer Type_x"] == "WHSL" and df["Price Code_x"] == "C" and df["Order Origin_x"] == "NUORDER" and df["Order Type_x"] in ["REGCB4", "REG4CB", "CBXRL", "REG", "NUORER"]:
    #     return "NuOder 60/40 CB"
    # elif df["Customer Type_x"] == "WHSL" and df["Price Code_x"] == "A" and (df["Order Origin_x"] is None or df["Order Origin_x"] not in ["NUORDER", "WEB"]) and df["Order Type_x"] in ["REG", "REGXRL", "DIR", "CPU", "INTER"]:
    #     return "WHSL 50/50 Purchase"
    # elif df["Customer Type_x"] == "WHSL" and df["Price Code_x"] == "A" and (df["Order Origin_x"] is None or df["Order Origin_x"] not in ["NUORDER", "WEB"]) and df["Order Type_x"] in ["REG", "REGXRL", "DIR", "CPU", "INTER"]:
    #     return "WHSL 50/50 Purchase"
    # elif df["Customer Type_x"] == "WHSL" and df["Price Code_x"] == "B" and (df["Order Origin_x"] is None or df["Order Origin_x"] not in ["NUORDER", "WEB"]) and df["Order Type_x"] in ["REG", "REGXRL", "DIR", "CPU", "INTER"]:
    #     return "WHSL 50/50 Purchase"
    # elif df["Customer Type_x"] == "WHSL" and df["Price Code_x"] == "B" and (df["Order Origin_x"] is None or df["Order Origin_x"] not in ["NUORDER", "WEB"]) and df["Order Type_x"] in ["REGCB4", "REG4CB", "CBXRL", "CBXNO", "DIR", "CPU"]:
    #     return "WHSL 50/50 CB"
    # elif df["Customer Type_x"] == "WHSL" and df["Price Code_x"] == "C" and (df["Order Origin_x"] is None or df["Order Origin_x"] not in ["NUORDER", "WEB"]) and df["Order Type_x"] in ["CBXRL", "CBXNO", "DIR", "CPU", "REG"]:
    #     return "WHSL 60/40 CB"
    # elif df["Customer Type_x"] == "DTC" and df["Price Code_x"] == "A" and df["Order Origin_x"] == "WEB" and df["Order Type_x"] in ["DIR", "REG"]:
    #     return "WEB"
    # elif df["Customer Type_x"] == "DTC-I" and df["Price Code_x"] == "A" and (df["Order Origin_x"] is None or df["Order Origin_x"] not in ["NUORDER", "WEB"]) and df["Order Type_x"] in ["REGXRL", "REG", "DIR", "CPU", "INTER"]:
    #     return "DTC Purchase"
    # elif df["Customer Type_x"] == "DTC" and df["Price Code_x"] == "A" and (df["Order Origin_x"] is None or df["Order Origin_x"] not in ["NUORDER", "WEB"]) and df["Order Type_x"] in ["REGXRL", "REG", "DIR", "CPU"]:
    #     return "DTC Purchase"
    # elif df["Customer Type_x"] == "DTC" and df["Price Code_x"] == "A" and (df["Order Origin_x"] is None or df["Order Origin_x"] not in ["NUORDER", "WEB"]) and df["Order Type_x"] in ["CBXNO", "CBXRL", "DIR", "CPU"]:
    #     return "DTC CB"
    # elif df["Customer Type_x"] == "DTC" and df["Price Code_x"] == "D" and (df["Order Origin_x"] is None or df["Order Origin_x"] not in ["NUORDER", "WEB"]) and df["Order Type_x"] in ["MKT", "REG", "DIR", "CPU"]:
    #     return "Charity/Marketing/Legal"
    # elif df["Customer Type_x"] == "EMP":
    #     return "Employee"
    # elif df["Order Type_x"] == "MKT":
    #     return "Charity/Marketing/Legal"
    # elif df["Customer Type_x"] == "WHSL" and df["Price Code_x"] == "B" and df["Order Origin_x"] == "NUORDER" and df["Order Type_x"] in ["NUORER", "REG", "REGXRL", "DIR", "CPU", "INTER"]:
    #     return "NuOder 50/50 Purchase"
    # else:
    #     return ""


def payment_terms(terms):
    if terms == "PAY-S":
        return "Due At Shipment"
    elif terms == "PAY-D":
        return "DOR"
    elif terms == "CB":
        return "Monthly Sales Report-Consignment"
    else:
        return ""

# 194642
sales_out["order_number"] = sales_in["Order No."]
sales_out["customer_name"] = sales_in["Customer Name"]
sales_out["season"] = ""
sales_out["channel"] = sales_in.apply(decide_channel, axis=1)
sales_out["order_date"] = sales_in["Order Date_x"]
sales_out["start_ship_date"] = sales_in["Start Date"]
sales_out["cancel_ship_date"] = sales_in["Cancel Date"]

sales_out["items_total"] = sales_in["Invoice Amt"]
sales_out["items_tax"] = ""
sales_out["subtotal"] = sales_in["Merch. Amt."]
sales_out["total_tax"] = sales_in["Tax Amt"]
sales_out["grand_total"] = sales_in["Invoice Amt"]
sales_out["total_quantity"] = ""
sales_out["shipping_tax"] = ""
sales_out["default_tax_rate"] = ""
sales_out["payment_terms"] = sales_in["Terms"].apply(payment_terms)


sales_out["currency"] = sales_in["Currency"]

sales_out["billing_contact"] = ""
sales_out["shipping_contact"] = ""

sales_out["billing_address_line_1"] = sales_in["BillTo Addr 1"]
sales_out["billing_address_line_2"] = sales_in["BillTo Addr 2"]
sales_out["billing_address_city"] = sales_in["Bill To City"]
sales_out["billing_address_state"] = sales_in["Bill To State"]
sales_out["billing_address_country"] = sales_in["Bill To Country"]
sales_out["billing_address_postcode"] = sales_in["Bill To Zip"]
sales_out["shipping_address_line_1"] = sales_in["ShipTo Addr 1"]
sales_out["shipping_address_line_2"] = sales_in["ShipTo Addr 2"]
sales_out["shipping_address_city"] = sales_in["Bill To City"]
sales_out["shipping_address_state"] = sales_in["Bill To State"]
sales_out["shipping_address_country"] = sales_in["Ship To Country"]
sales_out["shipping_address_postcode"] = sales_in["Ship To Zip"]
sales_out["product_name"] = sales_in["Style Name"]
sales_out["product_identifier"] = sales_in["Style"]
sales_out["ean_upc"] = sales_in["UPC"]
sales_out["color"] = sales_in["Color Desc"]
sales_out["salesman_1"] = sales_in["Salesman1 Name"]
sales_out["salesman_2"] = sales_in["Salesman2 Name"]
sales_out["unit_price"] = ""

sales_out["unit_tax"] = ""
sales_out["size"] = sales_in["Size"]
sales_out["quantity"] = sales_in["Qty Ord"]
sales_out["fulfillment_status"] = sales_in["Open Amt."].apply(lambda x: "Shipped" if x == 0 else "")
sales_out["warehouse"] = sales_in["Warehouse"]
sales_out["Open Amt."] = sales_in["Open Amt."]

sales_no_invoice = sales_out[((sales_out["Open Amt."].isna()) | (sales_out["Open Amt."] == ''))]
sales_shipped = sales_out[sales_out["Open Amt."] == 0]
sales_partially_paid = sales_out[sales_out["Open Amt."] != 0]

sales_out.drop(columns=["Open Amt."]).to_excel("COMPLETE_Uphance_Sales_Orders_16_7_2025.xlsx", index=False)
# sales_no_invoice.drop(columns=["Open Amt."]).to_excel("Uphance_Sales_Orders_without_invoice.xlsx", index=False)
# sales_shipped.drop(columns=["Open Amt."]).to_excel("Uphance_Sales_Orders_shipped.xlsx", index=False)
# sales_partially_paid.drop(columns=["Open Amt."]).to_excel("Uphance_Sales_Orders_partially_paid.xlsx", index=False)




